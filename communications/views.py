# -*- coding: utf-8 -*-
"""
站内通信视图 - 简化版
"""

from django.shortcuts import render, redirect
from django.http import JsonResponse
from django.views.generic import ListView
from django.views import View
from django.contrib import messages

from .models import Message, MessageRecipient, Announcement, MessageReadLog


class AdminRequiredMixin:
    """
    自定义管理员认证Mixin
    替代Django的LoginRequiredMixin，使用我们的认证系统
    """
    def dispatch(self, request, *args, **kwargs):
        # 检查用户是否已通过我们的认证中间件
        if not hasattr(request, 'current_staff') or not request.current_staff:
            messages.error(request, '请先登录')
            return redirect('organizations:admin:login')
        
        # 检查管理员权限
        if not request.current_staff.is_manager:
            messages.error(request, '您没有访问权限')
            return redirect('organizations:admin:login')
        
        return super().dispatch(request, *args, **kwargs)


class MessageCenterView(AdminRequiredMixin, View):
    """消息中心页面"""
    
    def get(self, request):
        """渲染消息列表页面"""
        current_staff = request.current_staff
        
        # 获取消息统计
        stats = self.get_message_stats(current_staff)
        
        # 配置表格列
        columns = [
            {
                'key': 'sender_name',
                'title': '发送者',
                'type': 'text',
                'sortable': True,
            },
            {
                'key': 'subject',
                'title': '主题',
                'type': 'text',
                'sortable': True,
            },
            {
                'key': 'priority_display',
                'title': '优先级',
                'type': 'badge',
                'variant_key': 'priority',
            },
            {
                'key': 'created_at',
                'title': '时间',
                'type': 'date',
                'format': 'Y-m-d H:i',
                'sortable': True,
            },
            {
                'key': 'read_status',
                'title': '状态',
                'type': 'status',
                'display_key': 'read_status_text',
            }
        ]
        
        # 配置搜索
        search_config = {
            'placeholder': '搜索消息...'
        }
        
        # 配置筛选器
        filters = [
            {
                'type': 'select',
                'name': 'priority',
                'placeholder': '优先级',
                'options': [
                    {'value': 'low', 'label': '低'},
                    {'value': 'normal', 'label': '普通'},
                    {'value': 'high', 'label': '高'},
                    {'value': 'urgent', 'label': '紧急'},
                ]
            },
            {
                'type': 'select',
                'name': 'is_read',
                'placeholder': '状态',
                'options': [
                    {'value': 'true', 'label': '已读'},
                    {'value': 'false', 'label': '未读'},
                ]
            }
        ]
        
        # 配置表格操作
        table_actions = [
            {
                'type': 'primary',
                'text': '刷新',
                'icon': 'refresh-cw',
                'onclick': 'window.messageManager.loadMessages()'
            }
        ]
        
        # 配置行操作
        row_actions = [
            {
                'type': 'dropdown',
                'items': [
                    {
                        'icon': 'eye',
                        'text': '查看',
                        'onclick': 'viewMessage(this.dataset.messageId)'
                    },
                    {
                        'icon': 'check',
                        'text': '标记已读',
                        'onclick': 'markAsRead(this.dataset.messageId)'
                    },
                    {
                        'icon': 'star',
                        'text': '加星标',
                        'onclick': 'toggleStar(this.dataset.messageId)'
                    },
                    {
                        'icon': 'trash-2',
                        'text': '删除',
                        'onclick': 'deleteMessage(this.dataset.messageId)'
                    }
                ]
            }
        ]
        
        # 优先级选项
        priority_options = [
            {'value': 'low', 'label': '低'},
            {'value': 'normal', 'label': '普通'},
            {'value': 'high', 'label': '高'},
            {'value': 'urgent', 'label': '紧急'},
        ]
        
        context = {
            'stats': stats,
            'columns': columns,
            'messages': [],  # 数据通过API异步加载
            'search_config': search_config,
            'filters': filters,
            'table_actions': table_actions,
            'row_actions': row_actions,
            'priority_options': priority_options,
            'pagination': {
                'current_page': 1,
                'total_pages': 1,
                'show_size_selector': True,
                'size_options': [10, 20, 50, 100],
                'current_size': 20
            },
            'table_info': {
                'total': 0,
                'start': 0,
                'end': 0
            }
        }
        
        return render(request, 'communications/message_list_simple.html', context)
    
    def get_message_stats(self, staff):
        """获取消息统计信息"""
        from django.db.models import Count, Q
        
        # 基础查询集
        base_queryset = MessageRecipient.objects.filter(
            recipient=staff,
            is_deleted=False
        )
        
        # 统计各种状态的消息数量
        stats = base_queryset.aggregate(
            total=Count('id'),
            unread=Count('id', filter=Q(is_read=False)),
            read=Count('id', filter=Q(is_read=True)),
            starred=Count('id', filter=Q(is_starred=True))
        )
        
        return stats


class MessageCenterOldView(AdminRequiredMixin, ListView):
    """原始消息中心页面（保留备用）"""
    model = MessageRecipient
    template_name = 'admin/communications/message_center.html'
    context_object_name = 'message_recipients'
    paginate_by = 20
    
    def get_queryset(self):
        """获取当前用户的消息"""
        # 获取当前用户
        current_staff = getattr(self.request, 'current_staff', None)
        if not current_staff:
            return MessageRecipient.objects.none()
        
        # 返回当前用户的消息，按创建时间倒序
        return MessageRecipient.objects.filter(
            recipient=current_staff,
            deleted_at__isnull=True
        ).select_related('message', 'message__sender').order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        
        # 获取当前用户
        current_staff = getattr(self.request, 'current_staff', None)
        if not current_staff:
            context['unread_count'] = 0
            context['total_count'] = 0
            context['type_stats'] = {}
            return context
        
        # 计算统计数据
        user_messages = MessageRecipient.objects.filter(
            recipient=current_staff,
            deleted_at__isnull=True
        )
        
        context['unread_count'] = user_messages.filter(is_read=False).count()
        context['total_count'] = user_messages.count()
        
        # 按消息类型统计
        from django.db.models import Count
        type_stats = user_messages.values('message__message_type').annotate(
            count=Count('id')
        )
        
        context['type_stats'] = {
            item['message__message_type']: item['count'] 
            for item in type_stats
        }
        
        return context


class MessageDetailView(AdminRequiredMixin, View):
    """消息详情页面"""
    
    def get(self, request, pk):
        return render(request, 'admin/communications/message_detail.html', {'message': None})


class MessageComposeView(AdminRequiredMixin, View):
    """消息编写页面"""
    
    def get(self, request):
        return render(request, 'admin/communications/message_compose.html')


class AnnouncementListView(AdminRequiredMixin, ListView):
    """公告列表页面"""
    model = Announcement
    template_name = 'admin/communications/announcement_list_simple.html'
    context_object_name = 'announcements'
    paginate_by = 20
    
    def get_queryset(self):
        return Announcement.objects.filter(
            deleted_at__isnull=True
        ).order_by('-is_pinned', '-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 计算统计数据
        all_announcements = self.get_queryset()
        context['total_count'] = all_announcements.count()
        context['published_count'] = all_announcements.filter(is_published=True).count()
        context['pinned_count'] = all_announcements.filter(is_pinned=True).count()
        context['high_priority_count'] = all_announcements.filter(is_pinned=True).count()
        
        return context


class MessageListAPIView(AdminRequiredMixin, View):
    """消息列表API"""
    
    def get(self, request):
        try:
            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = min(int(request.GET.get('page_size', 20)), 100)  # 限制最大50条
            message_type = request.GET.get('type', '')
            is_read = request.GET.get('is_read', '')
            priority = request.GET.get('priority', '')
            search = request.GET.get('search', '')
            
            # 构建查询集
            queryset = MessageRecipient.objects.filter(
                recipient=request.current_staff,
                is_deleted=False
            ).select_related('message', 'message__sender')
            
            # 应用筛选条件
            if message_type:
                queryset = queryset.filter(message__message_type=message_type)
            
            if is_read != '':
                queryset = queryset.filter(is_read=is_read.lower() == 'true')
            
            if priority:
                queryset = queryset.filter(message__priority=priority)
            
            if search:
                from django.db.models import Q
                queryset = queryset.filter(
                    Q(message__subject__icontains=search) |
                    Q(message__content__icontains=search) |
                    Q(message__sender__name__icontains=search)
                )
            
            # 排序
            queryset = queryset.order_by('-created_at')
            
            # 分页处理
            from django.core.paginator import Paginator
            paginator = Paginator(queryset, page_size)
            page_obj = paginator.get_page(page)
            
            # 构建消息数据
            messages = []
            for recipient in page_obj:
                message = recipient.message
                messages.append({
                    'id': message.id,
                    'recipient_id': recipient.id,
                    'subject': message.subject,
                    'content': message.content[:200] + '...' if len(message.content) > 200 else message.content,
                    'message_type': message.message_type,
                    'message_type_display': message.get_message_type_display(),
                    'priority': message.priority,
                    'priority_display': message.get_priority_display(),
                    'sender': {
                        'id': message.sender.id if message.sender else None,
                        'name': message.sender.name if message.sender else '系统',
                        'employee_no': message.sender.employee_no if message.sender else None
                    },
                    'is_read': recipient.is_read,
                    'is_starred': recipient.is_starred,
                    'read_at': recipient.read_at.strftime('%Y-%m-%d %H:%M:%S') if recipient.read_at else None,
                    'created_at': message.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'expires_at': message.expires_at.strftime('%Y-%m-%d %H:%M:%S') if message.expires_at else None,
                    'is_expired': message.is_expired,
                    'related_model': message.related_model,
                    'related_id': message.related_id
                })
            
            # 构建分页信息
            pagination = {
                'current_page': page_obj.number,
                'total_pages': paginator.num_pages,
                'total_count': paginator.count,
                'page_size': page_size,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
                'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
                'previous_page': page_obj.previous_page_number() if page_obj.has_previous() else None
            }
            
            return JsonResponse({
                'success': True,
                'messages': messages,
                'pagination': pagination,
                'filters': {
                    'message_types': Message.MESSAGE_TYPES,
                    'priorities': Message.PRIORITY_CHOICES
                }
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'获取消息列表失败: {str(e)}',
                'messages': [],
                'pagination': {}
            })


class UnreadMessageCountAPIView(AdminRequiredMixin, View):
    """未读消息数量API"""
    
    def get(self, request):
        try:
            # 暂时返回固定值，避免认证问题
            unread_count = 0
            if hasattr(request, 'current_staff') and request.current_staff:
                unread_count = MessageRecipient.objects.filter(
                    recipient=request.current_staff,
                    is_read=False,
                    is_deleted=False
                ).count()
            
            return JsonResponse({
                'success': True,
                'count': unread_count
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e),
                'count': 0
            })


class StaffDataAPIView(AdminRequiredMixin, View):
    """员工数据API，提供部门和员工信息"""
    
    def get(self, request):
        try:
            from organizations.models import Department, Staff
            
            # 获取所有活跃部门和员工
            departments = Department.objects.filter(
                is_active=True,
                deleted_at__isnull=True
            ).prefetch_related('staff_set').order_by('sort_order', 'name')
            
            department_data = {}
            
            for dept in departments:
                # 获取部门下的活跃员工
                staff_list = dept.staff_set.filter(
                    is_active=True,
                    deleted_at__isnull=True
                ).order_by('employee_no')
                
                staff_data = []
                for staff in staff_list:
                    staff_data.append({
                        'id': staff.id,
                        'name': staff.name,
                        'employee_no': staff.employee_no,
                        'position': staff.position.name if staff.position else '未设置',
                        'email': staff.email or '',
                        'phone': staff.phone or ''
                    })
                
                department_data[dept.name] = staff_data
            
            return JsonResponse({
                'success': True,
                'departments': department_data
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'获取员工数据失败: {str(e)}',
                'departments': {}
            })

# 其他简化的API视图
class MessageDetailAPIView(AdminRequiredMixin, View):
    """消息详情API"""
    
    def get(self, request, pk):
        try:
            # 获取消息接收记录
            recipient = MessageRecipient.objects.select_related(
                'message', 'message__sender'
            ).get(
                message_id=pk,
                recipient=request.current_staff,
                is_deleted=False
            )
            
            message = recipient.message
            
            # 构建消息详情数据
            message_data = {
                'id': message.id,
                'recipient_id': recipient.id,
                'subject': message.subject,
                'content': message.content,
                'message_type': message.message_type,
                'message_type_display': message.get_message_type_display(),
                'priority': message.priority,
                'priority_display': message.get_priority_display(),
                'sender': {
                    'id': message.sender.id if message.sender else None,
                    'name': message.sender.name if message.sender else '系统',
                    'employee_no': message.sender.employee_no if message.sender else None,
                    'department': message.sender.department.name if message.sender and message.sender.department else None
                },
                'recipient_info': {
                    'is_read': recipient.is_read,
                    'is_starred': recipient.is_starred,
                    'read_at': recipient.read_at.strftime('%Y-%m-%d %H:%M:%S') if recipient.read_at else None,
                    'received_at': recipient.created_at.strftime('%Y-%m-%d %H:%M:%S')
                },
                'message_info': {
                    'created_at': message.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'expires_at': message.expires_at.strftime('%Y-%m-%d %H:%M:%S') if message.expires_at else None,
                    'is_expired': message.is_expired,
                    'is_broadcast': message.is_broadcast,
                    'related_model': message.related_model,
                    'related_id': message.related_id,
                    'total_recipients': message.get_total_recipients(),
                    'unread_recipients': message.get_unread_count()
                }
            }
            
            # 自动标记为已读（如果尚未读取）
            if not recipient.is_read:
                recipient.mark_as_read()
                
                # 创建阅读日志
                MessageReadLog.objects.create(
                    message=message,
                    reader=request.current_staff,
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')[:500]
                )
            
            return JsonResponse({
                'success': True,
                'message': message_data
            })
            
        except MessageRecipient.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': '消息不存在或您没有查看权限',
                'code': 'MESSAGE_NOT_FOUND'
            }, status=404)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'获取消息详情失败: {str(e)}',
                'code': 'INTERNAL_ERROR'
            }, status=500)
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip

class MessageSendAPIView(AdminRequiredMixin, View):
    """消息发送API"""
    
    def post(self, request):
        try:
            import json
            from organizations.models import Staff
            
            # 解析请求数据
            data = json.loads(request.body)
            
            # 验证必填字段
            required_fields = ['message_type', 'priority', 'subject', 'content', 'recipients']
            for field in required_fields:
                if not data.get(field):
                    return JsonResponse({
                        'success': False,
                        'error': f'缺少必填字段: {field}'
                    })
            
            # 验证收件人
            recipient_ids = data.get('recipients', [])
            if not recipient_ids:
                return JsonResponse({
                    'success': False,
                    'error': '请选择至少一个收件人'
                })
            
            # 获取收件人对象
            recipients = Staff.objects.filter(
                id__in=recipient_ids,
                is_active=True,
                deleted_at__isnull=True
            )
            
            if not recipients.exists():
                return JsonResponse({
                    'success': False,
                    'error': '选择的收件人无效或不存在'
                })
            
            # 创建消息
            message = Message.objects.create(
                sender=request.current_staff,
                message_type=data['message_type'],
                priority=data['priority'],
                subject=data['subject'],
                content=data['content'],
                expires_at=data.get('expires_at'),
                related_model=data.get('related_model', ''),
                related_id=data.get('related_id')
            )
            
            # 创建消息接收者记录
            message_recipients = []
            for recipient in recipients:
                message_recipients.append(MessageRecipient(
                    message=message,
                    recipient=recipient
                ))
            
            MessageRecipient.objects.bulk_create(message_recipients)
            
            return JsonResponse({
                'success': True,
                'message': '消息发送成功',
                'message_id': message.id,
                'recipient_count': len(message_recipients)
            })
            
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': '无效的JSON数据格式'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'发送失败: {str(e)}'
            })

class MessageMarkReadAPIView(AdminRequiredMixin, View):
    """标记消息已读/未读API"""
    
    def post(self, request, pk):
        try:
            import json
            
            # 解析请求数据
            data = json.loads(request.body) if request.body else {}
            is_read = data.get('is_read', True)  # 默认标记为已读
            
            # 获取消息接收记录
            recipient = MessageRecipient.objects.select_related('message').get(
                message_id=pk,
                recipient=request.current_staff,
                is_deleted=False
            )
            
            # 更新阅读状态
            if is_read and not recipient.is_read:
                recipient.mark_as_read()
                
                # 创建阅读日志
                MessageReadLog.objects.create(
                    message=recipient.message,
                    reader=request.current_staff,
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')[:500]
                )
                
                action = '标记为已读'
                
            elif not is_read and recipient.is_read:
                recipient.mark_as_unread()
                action = '标记为未读'
                
            else:
                action = '状态未变更'
            
            return JsonResponse({
                'success': True,
                'message': f'消息{action}成功',
                'data': {
                    'message_id': pk,
                    'is_read': recipient.is_read,
                    'read_at': recipient.read_at.strftime('%Y-%m-%d %H:%M:%S') if recipient.read_at else None
                }
            })
            
        except MessageRecipient.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': '消息不存在或您没有操作权限',
                'code': 'MESSAGE_NOT_FOUND'
            }, status=404)
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': '请求数据格式错误',
                'code': 'INVALID_JSON'
            }, status=400)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'标记失败: {str(e)}',
                'code': 'INTERNAL_ERROR'
            }, status=500)
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip

class BatchMarkReadAPIView(AdminRequiredMixin, View):
    """批量标记已读API"""
    
    def post(self, request):
        try:
            import json
            from django.utils import timezone
            
            # 解析请求数据
            data = json.loads(request.body) if request.body else {}
            
            # 获取操作参数
            action = data.get('action', 'mark_read')  # mark_read, mark_unread, mark_all_read
            message_ids = data.get('message_ids', [])
            message_type = data.get('message_type', '')
            priority = data.get('priority', '')
            is_read = data.get('is_read', True)
            
            # 构建基础查询集
            queryset = MessageRecipient.objects.filter(
                recipient=request.current_staff,
                is_deleted=False
            ).select_related('message')
            
            if action == 'mark_all_read':
                # 全部标记为已读
                queryset = queryset.filter(is_read=False)
                
                # 应用筛选条件
                if message_type:
                    queryset = queryset.filter(message__message_type=message_type)
                
                if priority:
                    queryset = queryset.filter(message__priority=priority)
                
            elif action in ['mark_read', 'mark_unread']:
                # 根据消息ID列表标记
                if not message_ids:
                    return JsonResponse({
                        'success': False,
                        'error': '请提供要操作的消息ID列表',
                        'code': 'MISSING_MESSAGE_IDS'
                    }, status=400)
                
                queryset = queryset.filter(message_id__in=message_ids)
                
                # 根据操作类型过滤
                if action == 'mark_read':
                    queryset = queryset.filter(is_read=False)
                else:  # mark_unread
                    queryset = queryset.filter(is_read=True)
                    is_read = False
                    
            else:
                return JsonResponse({
                    'success': False,
                    'error': f'不支持的操作类型: {action}',
                    'code': 'INVALID_ACTION'
                }, status=400)
            
            # 执行批量操作
            recipients_to_update = list(queryset)
            updated_count = 0
            
            if recipients_to_update:
                # 批量更新
                now = timezone.now()
                
                for recipient in recipients_to_update:
                    if is_read and not recipient.is_read:
                        # 标记为已读
                        recipient.is_read = True
                        recipient.read_at = now
                        updated_count += 1
                        
                        # 创建阅读日志
                        MessageReadLog.objects.create(
                            message=recipient.message,
                            reader=request.current_staff,
                            ip_address=self._get_client_ip(request),
                            user_agent=request.META.get('HTTP_USER_AGENT', '')[:500]
                        )
                        
                    elif not is_read and recipient.is_read:
                        # 标记为未读
                        recipient.is_read = False
                        recipient.read_at = None
                        updated_count += 1
                
                # 批量保存
                if updated_count > 0:
                    MessageRecipient.objects.bulk_update(
                        recipients_to_update, 
                        ['is_read', 'read_at']
                    )
            
            # 构建响应消息
            action_messages = {
                'mark_read': '标记为已读',
                'mark_unread': '标记为未读',
                'mark_all_read': '全部标记为已读'
            }
            
            action_message = action_messages.get(action, '操作')
            
            return JsonResponse({
                'success': True,
                'message': f'{action_message}成功',
                'data': {
                    'action': action,
                    'updated_count': updated_count,
                    'total_selected': len(message_ids) if message_ids else len(recipients_to_update),
                    'remaining_unread': MessageRecipient.objects.filter(
                        recipient=request.current_staff,
                        is_read=False,
                        is_deleted=False
                    ).count()
                }
            })
            
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': '请求数据格式错误',
                'code': 'INVALID_JSON'
            }, status=400)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'批量操作失败: {str(e)}',
                'code': 'INTERNAL_ERROR'
            }, status=500)
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip

class MessagePollAPIView(AdminRequiredMixin, View):
    def get(self, request):
        return JsonResponse({'new_messages': [], 'unread_count': 0, 'current_time': ''})

class AnnouncementListAPIView(AdminRequiredMixin, View):
    """公告列表API"""
    
    def get(self, request):
        try:
            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = min(int(request.GET.get('page_size', 20)), 100)
            announcement_type = request.GET.get('type', '')
            is_published = request.GET.get('is_published', '')
            is_pinned = request.GET.get('is_pinned', '')
            search = request.GET.get('search', '')
            include_expired = request.GET.get('include_expired', 'false').lower() == 'true'
            
            # 构建查询集
            queryset = Announcement.objects.filter(
                deleted_at__isnull=True
            ).select_related('author', 'target_department')
            
            # 权限过滤：只显示用户可以查看的公告
            staff = request.current_staff
            if not staff.is_super_admin:
                # 普通用户只能看到：全公司公告 + 本部门公告 + 已发布的公告
                from django.db.models import Q
                queryset = queryset.filter(
                    Q(target_department__isnull=True) |  # 全公司公告
                    Q(target_department=staff.department)  # 本部门公告
                ).filter(is_published=True)
            
            # 应用筛选条件
            if announcement_type:
                queryset = queryset.filter(announcement_type=announcement_type)
            
            if is_published != '':
                queryset = queryset.filter(is_published=is_published.lower() == 'true')
            
            if is_pinned != '':
                queryset = queryset.filter(is_pinned=is_pinned.lower() == 'true')
            
            if search:
                from django.db.models import Q
                queryset = queryset.filter(
                    Q(title__icontains=search) |
                    Q(content__icontains=search) |
                    Q(author__name__icontains=search)
                )
            
            # 是否包含过期公告
            if not include_expired:
                from django.utils import timezone
                now = timezone.now()
                queryset = queryset.filter(
                    Q(expire_at__isnull=True) |
                    Q(expire_at__gte=now)
                )
            
            # 排序：置顶的在前，然后按创建时间倒序
            queryset = queryset.order_by('-is_pinned', '-created_at')
            
            # 分页处理
            from django.core.paginator import Paginator
            paginator = Paginator(queryset, page_size)
            page_obj = paginator.get_page(page)
            
            # 构建公告数据
            announcements = []
            for announcement in page_obj:
                announcements.append({
                    'id': announcement.id,
                    'title': announcement.title,
                    'content': announcement.content[:300] + '...' if len(announcement.content) > 300 else announcement.content,
                    'announcement_type': announcement.announcement_type,
                    'announcement_type_display': announcement.get_announcement_type_display(),
                    'author': {
                        'id': announcement.author.id if announcement.author else None,
                        'name': announcement.author.name if announcement.author else '系统',
                        'department': announcement.author.department.name if announcement.author and announcement.author.department else None
                    },
                    'target_department': {
                        'id': announcement.target_department.id if announcement.target_department else None,
                        'name': announcement.target_department.name if announcement.target_department else '全公司'
                    },
                    'is_published': announcement.is_published,
                    'is_pinned': announcement.is_pinned,
                    'is_html': announcement.is_html,
                    'view_count': announcement.view_count,
                    'publish_at': announcement.publish_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'expire_at': announcement.expire_at.strftime('%Y-%m-%d %H:%M:%S') if announcement.expire_at else None,
                    'created_at': announcement.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'is_expired': announcement.is_expired,
                    'is_active': announcement.is_active
                })
            
            # 构建分页信息
            pagination = {
                'current_page': page_obj.number,
                'total_pages': paginator.num_pages,
                'total_count': paginator.count,
                'page_size': page_size,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
                'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
                'previous_page': page_obj.previous_page_number() if page_obj.has_previous() else None
            }
            
            # 统计信息
            total_queryset = Announcement.objects.filter(deleted_at__isnull=True)
            if not staff.is_super_admin:
                from django.db.models import Q
                total_queryset = total_queryset.filter(
                    Q(target_department__isnull=True) |
                    Q(target_department=staff.department)
                ).filter(is_published=True)
            
            stats = {
                'total_count': total_queryset.count(),
                'published_count': total_queryset.filter(is_published=True).count(),
                'pinned_count': total_queryset.filter(is_pinned=True).count(),
                'expired_count': total_queryset.filter(is_published=True).exclude(
                    expire_at__isnull=True
                ).filter(expire_at__lt=timezone.now()).count() if 'timezone' in locals() else 0
            }
            
            return JsonResponse({
                'success': True,
                'announcements': announcements,
                'pagination': pagination,
                'stats': stats,
                'filters': {
                    'announcement_types': Announcement.ANNOUNCEMENT_TYPES,
                    'user_permissions': {
                        'can_create': staff.is_manager,
                        'can_edit_all': staff.is_super_admin,
                        'can_delete': staff.is_super_admin
                    }
                }
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'获取公告列表失败: {str(e)}',
                'announcements': [],
                'pagination': {}
            })

class AnnouncementDetailAPIView(AdminRequiredMixin, View):
    """公告详情API"""
    
    def get(self, request, pk):
        try:
            # 获取公告对象
            queryset = Announcement.objects.filter(
                deleted_at__isnull=True
            ).select_related('author', 'target_department')
            
            announcement = queryset.get(id=pk)
            
            # 权限检查：确保用户有权查看此公告
            staff = request.current_staff
            if not staff.is_super_admin:
                # 普通用户只能查看：全公司公告 + 本部门公告 + 已发布的公告
                if announcement.target_department and announcement.target_department != staff.department:
                    return JsonResponse({
                        'success': False,
                        'error': '您没有权限查看此公告',
                        'code': 'PERMISSION_DENIED'
                    }, status=403)
                    
                if not announcement.is_published:
                    return JsonResponse({
                        'success': False,
                        'error': '公告未发布或已下线',
                        'code': 'ANNOUNCEMENT_NOT_PUBLISHED'
                    }, status=404)
            
            # 增加查看次数
            announcement.increment_view_count()
            
            # 构建公告详情数据
            announcement_data = {
                'id': announcement.id,
                'title': announcement.title,
                'content': announcement.content,
                'announcement_type': announcement.announcement_type,
                'announcement_type_display': announcement.get_announcement_type_display(),
                'author': {
                    'id': announcement.author.id if announcement.author else None,
                    'name': announcement.author.name if announcement.author else '系统',
                    'employee_no': announcement.author.employee_no if announcement.author else None,
                    'department': announcement.author.department.name if announcement.author and announcement.author.department else None,
                    'position': announcement.author.position.name if announcement.author and announcement.author.position else None
                },
                'target_department': {
                    'id': announcement.target_department.id if announcement.target_department else None,
                    'name': announcement.target_department.name if announcement.target_department else '全公司',
                    'description': announcement.target_department.description if announcement.target_department else None
                },
                'status': {
                    'is_published': announcement.is_published,
                    'is_pinned': announcement.is_pinned,
                    'is_expired': announcement.is_expired,
                    'is_active': announcement.is_active
                },
                'content_info': {
                    'is_html': announcement.is_html,
                    'content_length': len(announcement.content),
                    'view_count': announcement.view_count
                },
                'time_info': {
                    'created_at': announcement.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'publish_at': announcement.publish_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'expire_at': announcement.expire_at.strftime('%Y-%m-%d %H:%M:%S') if announcement.expire_at else None
                },
                'permissions': {
                    'can_edit': (staff.is_super_admin or 
                               (staff.is_manager and announcement.author_id == staff.id)),
                    'can_delete': staff.is_super_admin,
                    'can_pin': staff.is_super_admin,
                    'can_unpublish': (staff.is_super_admin or 
                                    (staff.is_manager and announcement.author_id == staff.id))
                }
            }
            
            return JsonResponse({
                'success': True,
                'announcement': announcement_data
            })
            
        except Announcement.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': '公告不存在或已被删除',
                'code': 'ANNOUNCEMENT_NOT_FOUND'
            }, status=404)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'获取公告详情失败: {str(e)}',
                'code': 'INTERNAL_ERROR'
            }, status=500)

class AnnouncementCreateAPIView(AdminRequiredMixin, View):
    """公告创建API"""
    
    def post(self, request):
        try:
            import json
            from datetime import datetime
            from django.utils import timezone
            from organizations.models import Department
            
            # 权限检查：只有管理员可以创建公告
            if not request.current_staff.is_manager:
                return JsonResponse({
                    'success': False,
                    'error': '您没有权限创建公告',
                    'code': 'PERMISSION_DENIED'
                }, status=403)
            
            # 解析请求数据
            data = json.loads(request.body)
            
            # 验证必填字段
            required_fields = ['title', 'content', 'announcement_type']
            for field in required_fields:
                if not data.get(field):
                    return JsonResponse({
                        'success': False,
                        'error': f'缺少必填字段: {field}',
                        'code': 'MISSING_REQUIRED_FIELD'
                    }, status=400)
            
            # 验证公告类型
            valid_types = [choice[0] for choice in Announcement.ANNOUNCEMENT_TYPES]
            if data['announcement_type'] not in valid_types:
                return JsonResponse({
                    'success': False,
                    'error': f'无效的公告类型: {data["announcement_type"]}',
                    'code': 'INVALID_ANNOUNCEMENT_TYPE'
                }, status=400)
            
            # 处理目标部门
            target_department = None
            if data.get('target_department_id'):
                try:
                    target_department = Department.objects.get(
                        id=data['target_department_id'],
                        is_active=True,
                        deleted_at__isnull=True
                    )
                    
                    # 权限检查：非超级管理员只能创建自己部门的公告
                    if not request.current_staff.is_super_admin:
                        if target_department != request.current_staff.department:
                            return JsonResponse({
                                'success': False,
                                'error': '您只能为自己的部门创建公告',
                                'code': 'DEPARTMENT_PERMISSION_DENIED'
                            }, status=403)
                            
                except Department.DoesNotExist:
                    return JsonResponse({
                        'success': False,
                        'error': '指定的目标部门不存在',
                        'code': 'DEPARTMENT_NOT_FOUND'
                    }, status=400)
            
            # 处理发布时间
            publish_at = timezone.now()
            if data.get('publish_at'):
                try:
                    publish_at = datetime.strptime(data['publish_at'], '%Y-%m-%d %H:%M:%S')
                    publish_at = timezone.make_aware(publish_at) if timezone.is_naive(publish_at) else publish_at
                except ValueError:
                    return JsonResponse({
                        'success': False,
                        'error': '发布时间格式错误，应为: YYYY-MM-DD HH:MM:SS',
                        'code': 'INVALID_PUBLISH_TIME'
                    }, status=400)
            
            # 处理过期时间
            expire_at = None
            if data.get('expire_at'):
                try:
                    expire_at = datetime.strptime(data['expire_at'], '%Y-%m-%d %H:%M:%S')
                    expire_at = timezone.make_aware(expire_at) if timezone.is_naive(expire_at) else expire_at
                    
                    # 验证过期时间不能早于发布时间
                    if expire_at <= publish_at:
                        return JsonResponse({
                            'success': False,
                            'error': '过期时间不能早于或等于发布时间',
                            'code': 'INVALID_EXPIRE_TIME'
                        }, status=400)
                        
                except ValueError:
                    return JsonResponse({
                        'success': False,
                        'error': '过期时间格式错误，应为: YYYY-MM-DD HH:MM:SS',
                        'code': 'INVALID_EXPIRE_TIME_FORMAT'
                    }, status=400)
            
            # 创建公告
            announcement = Announcement.objects.create(
                title=data['title'],
                content=data['content'],
                announcement_type=data['announcement_type'],
                author=request.current_staff,
                target_department=target_department,
                is_published=data.get('is_published', True),
                is_pinned=data.get('is_pinned', False) and request.current_staff.is_super_admin,  # 只有超级管理员可以置顶
                is_html=data.get('is_html', False),
                publish_at=publish_at,
                expire_at=expire_at
            )
            
            return JsonResponse({
                'success': True,
                'message': '公告创建成功',
                'data': {
                    'announcement_id': announcement.id,
                    'title': announcement.title,
                    'announcement_type': announcement.announcement_type,
                    'is_published': announcement.is_published,
                    'is_pinned': announcement.is_pinned,
                    'target_scope': announcement.target_department.name if announcement.target_department else '全公司',
                    'publish_at': announcement.publish_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'expire_at': announcement.expire_at.strftime('%Y-%m-%d %H:%M:%S') if announcement.expire_at else None
                }
            })
            
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': '请求数据格式错误',
                'code': 'INVALID_JSON'
            }, status=400)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'创建公告失败: {str(e)}',
                'code': 'INTERNAL_ERROR'
            }, status=500)

class AnnouncementDeleteAPIView(AdminRequiredMixin, View):
    """公告删除API"""
    
    def post(self, request, pk):
        try:
            # 获取公告对象
            announcement = Announcement.objects.get(
                id=pk,
                deleted_at__isnull=True
            )
            
            # 软删除公告
            from django.utils import timezone
            announcement.deleted_at = timezone.now()
            announcement.save(update_fields=['deleted_at'])
            
            return JsonResponse({
                'success': True,
                'message': '公告删除成功'
            })
            
        except Announcement.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': '公告不存在或已被删除'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'删除失败: {str(e)}'
            })

class RecentNotificationsAPIView(AdminRequiredMixin, View):
    def get(self, request):
        return JsonResponse({'notifications': [], 'total_unread_messages': 0})

class RecentMessagesAPIView(AdminRequiredMixin, View):
    """最近消息API"""
    
    def get(self, request):
        try:
            messages = []
            if hasattr(request, 'current_staff') and request.current_staff:
                # 获取最近5条消息
                recent_recipients = MessageRecipient.objects.filter(
                    recipient=request.current_staff,
                    is_deleted=False
                ).select_related('message', 'message__sender').order_by('-created_at')[:5]
                
                for recipient in recent_recipients:
                    message = recipient.message
                    messages.append({
                        'id': message.id,
                        'subject': message.subject,
                        'message_type': message.message_type,
                        'sender_name': message.sender.name if message.sender else '系统',
                        'created_at': message.created_at.strftime('%m-%d %H:%M'),
                        'is_read': recipient.is_read,
                        'priority': message.priority
                    })
            
            return JsonResponse({
                'success': True,
                'messages': messages
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e),
                'messages': []
            })

# 创建、更新、详情页面的占位符视图
class AnnouncementCreateView(AdminRequiredMixin, View):
    def get(self, request):
        return render(request, 'admin/communications/announcement_create.html')

class AnnouncementDetailView(AdminRequiredMixin, View):
    def get(self, request, pk):
        return render(request, 'admin/communications/announcement_detail.html')

class AnnouncementUpdateView(AdminRequiredMixin, View):
    def get(self, request, pk):
        return render(request, 'admin/communications/announcement_update.html')

class NotificationTemplateListView(AdminRequiredMixin, View):
    def get(self, request):
        return render(request, 'admin/communications/template_list.html')

class NotificationTemplateCreateView(AdminRequiredMixin, View):
    def get(self, request):
        return render(request, 'admin/communications/template_create.html')

class NotificationTemplateUpdateView(AdminRequiredMixin, View):
    def get(self, request, pk):
        return render(request, 'admin/communications/template_update.html')

# 匿名端视图占位符
class AnonymousMessageListView(View):
    def get(self, request):
        return render(request, 'anonymous/communications/message_list.html')

class AnonymousNotificationListView(View):
    def get(self, request):
        return render(request, 'anonymous/communications/notification_list.html')
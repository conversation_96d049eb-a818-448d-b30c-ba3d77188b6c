{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}员工管理{% endblock %}
{% block page_description %}管理公司员工信息和权限设置{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'organizations:admin:staff_import' %}" class="btn btn-secondary btn-md flex items-center space-x-2">
        <i data-lucide="upload" class="w-4 h-4"></i>
        <span>导入员工</span>
    </a>
    <a href="{% url 'organizations:admin:staff_export' %}" class="btn btn-secondary btn-md flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>导出员工</span>
    </a>
    <a href="{% url 'organizations:admin:staff_create' %}" class="btn btn-primary btn-md flex items-center space-x-2">
        <i data-lucide="plus" class="w-4 h-4"></i>
        <span>新建员工</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<!-- Stats -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="card card-body">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总员工数</p>
                <p class="text-2xl font-bold text-gray-900">{{ object_list|length|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="card card-body">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="user-check" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">在职员工</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.active_staff|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="card card-body">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="shield-check" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">管理人员</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.admin_staff|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="card card-body">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="building" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">部门数量</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_departments|default:0 }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card card-body mb-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="relative">
            <input type="text" id="searchInput" placeholder="搜索员工姓名、编号或邮箱..." class="form-input pl-10" data-table-search="staffTable">
            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
        </div>
        <select id="departmentFilter" class="form-select">
            <option value="">所有部门</option>
            {% for dept in departments %}
            <option value="{{ dept.id }}">{{ dept.name }}</option>
            {% endfor %}
        </select>
        <select id="positionFilter" class="form-select">
            <option value="">所有职位</option>
            <option value="management">管理人员</option>
            <option value="regular">普通员工</option>
        </select>
    </div>
</div>

<!-- Batch Operations Toolbar (Hidden by default) -->
<div id="batchToolbar" class="card card-body mb-6 hidden">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
                <i data-lucide="check-square" class="w-5 h-5 text-blue-600"></i>
                <span class="text-sm font-medium text-gray-700">已选择 <span id="selectedCount" class="text-blue-600 font-bold">0</span> 个员工</span>
            </div>
            <button onclick="clearSelection()" class="text-sm text-gray-500 hover:text-gray-700">
                <i data-lucide="x" class="w-4 h-4 inline mr-1"></i>清空选择
            </button>
        </div>
        <div class="flex items-center space-x-2">
            <div class="relative" id="batchActionsDropdown">
                <button onclick="toggleBatchActionsDropdown()" class="btn btn-primary btn-sm flex items-center space-x-2">
                    <i data-lucide="settings" class="w-4 h-4"></i>
                    <span>批量操作</span>
                    <i data-lucide="chevron-down" class="w-4 h-4"></i>
                </button>
                <div id="batchActionsMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200 hidden">
                    <div class="py-1">
                        <button onclick="batchChangeStatus(true)" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i data-lucide="user-check" class="w-4 h-4 mr-2 text-green-600"></i>
                            批量启用
                        </button>
                        <button onclick="batchChangeStatus(false)" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i data-lucide="user-x" class="w-4 h-4 mr-2 text-orange-600"></i>
                            批量禁用
                        </button>
                        <div class="border-t border-gray-100"></div>
                        <button onclick="batchChangeDepartment()" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i data-lucide="building" class="w-4 h-4 mr-2 text-blue-600"></i>
                            批量分配部门
                        </button>
                        <button onclick="batchChangeRole()" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i data-lucide="shield" class="w-4 h-4 mr-2 text-purple-600"></i>
                            批量分配角色
                        </button>
                        <div class="border-t border-gray-100"></div>
                        <button onclick="batchExport()" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i data-lucide="download" class="w-4 h-4 mr-2 text-indigo-600"></i>
                            导出选中员工
                        </button>
                        <div class="border-t border-gray-100"></div>
                        <button onclick="batchDelete()" class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                            <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                            批量删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Staff Table -->
<div class="card">
    <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">员工列表</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="table" data-table-optimize="true" id="staffTable">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left">
                        <input type="checkbox" id="selectAll" class="checkbox" title="全选/取消全选">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-sortable="name">员工信息</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-sortable="department">所属部门</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-sortable="position">职位信息</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-sortable="is_active">状态</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">匿名编号</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="staffTableBody">
            {% if object_list %}
                {% for staff in object_list %}
                <tr data-department="{{ staff.department.id }}" 
                    data-position="{% if staff.position %}{{ staff.position.level }}{% else %}0{% endif %}"
                    data-staff-id="{{ staff.id }}">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" class="staff-checkbox checkbox" 
                               value="{{ staff.id }}" data-staff-name="{{ staff.name }}">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium">{{ staff.name|first|default:"员" }}</span>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">{{ staff.name }}</div>
                                <div class="text-sm text-gray-500">{{ staff.employee_no }}</div>
                                <div class="text-sm text-gray-500">{{ staff.email|default:"未设置邮箱" }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ staff.department.name }}</div>
                        <div class="text-sm text-gray-500">{{ staff.department.dept_code }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if staff.position %}
                            <div class="text-sm text-gray-900">{{ staff.position.name }}</div>
                            <div class="text-sm text-gray-500">等级{{ staff.position.level }}级</div>
                        {% else %}
                            <div class="text-sm text-gray-400">无职位</div>
                            <div class="text-sm text-gray-500">普通员工（1-4级）</div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if staff.is_active %}
                            <span class="badge badge-success">在职</span>
                        {% else %}
                            <span class="badge badge-error">离职</span>
                        {% endif %}
                        {% if staff.is_admin %}
                            <span class="badge badge-primary ml-1">管理员</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            {% if staff.new_anonymous_code %}
                                <span class="font-mono">{{ staff.new_anonymous_code }}</span>
                                <span class="badge badge-success ml-2">安全</span>
                            {% elif staff.anonymous_code %}
                                <span class="font-mono">{{ staff.anonymous_code }}</span>
                                <span class="badge badge-warning ml-2">待升级</span>
                            {% else %}
                                <span class="text-gray-400">未生成</span>
                            {% endif %}
                        </div>
                        {% if staff.new_anonymous_code or staff.anonymous_code %}
                            <div class="text-sm text-gray-500">可匿名登录</div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <a href="{% url 'organizations:admin:staff_detail' staff.pk %}" class="text-blue-600 hover:text-blue-900">查看</a>
                        <a href="{% url 'organizations:admin:staff_update' staff.pk %}" class="text-indigo-600 hover:text-indigo-900">编辑</a>
                        <a href="{% url 'organizations:admin:staff_reset_password' staff.pk %}" class="text-yellow-600 hover:text-yellow-900">重置密码</a>
                        <button onclick="confirmDelete('{{ staff.pk }}', '{{ staff.name }}')" class="text-red-600 hover:text-red-900">删除</button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center">
                        <i data-lucide="users" class="mx-auto h-12 w-12 text-gray-400"></i>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无员工数据</h3>
                        <p class="mt-1 text-sm text-gray-500">开始添加第一个员工吧。</p>
                        <div class="mt-6">
                            <a href="{% url 'organizations:admin:staff_create' %}" class="btn btn-primary btn-md inline-flex items-center">
                                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                新建员工
                            </a>
                        </div>
                    </td>
                </tr>
            {% endif %}
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="card card-body flex items-center justify-between mt-6">
    <div class="flex-1 flex justify-between sm:hidden">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}" class="btn btn-secondary btn-sm">上一页</a>
        {% endif %}
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="ml-3 btn btn-secondary btn-sm">下一页</a>
        {% endif %}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                显示第 <span class="font-medium">{{ page_obj.start_index }}</span> 到 <span class="font-medium">{{ page_obj.end_index }}</span> 条，
                共 <span class="font-medium">{{ page_obj.paginator.count }}</span> 条记录
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="btn btn-ghost btn-sm rounded-l-md">
                        <i data-lucide="chevron-left" class="w-4 h-4"></i>
                    </a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">{{ num }}</span>
                    {% else %}
                        <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">{{ num }}</a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="btn btn-ghost btn-sm rounded-r-md">
                        <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
    </div>
</div>
{% endif %}

<!-- Batch Department Assignment Modal -->
<div id="batchDepartmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">批量分配部门</h3>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">目标部门</label>
                    <select id="batchDepartmentSelect" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        {% for dept in departments %}
                        <option value="{{ dept.id }}">{{ dept.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-4">
                    <p class="text-sm text-gray-600">将选中的 <span id="batchDeptCount">0</span> 个员工分配到所选部门</p>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeBatchDepartmentModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">取消</button>
                <button onclick="confirmBatchDepartment()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">确认分配</button>
            </div>
        </div>
    </div>
</div>

<!-- Batch Role Assignment Modal -->
<div id="batchRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">批量分配角色</h3>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">目标角色</label>
                    <select id="batchRoleSelect" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="employee">员工</option>
                        <option value="admin">管理员</option>
                        <option value="dept_manager">部门经理</option>
                        <option value="eval_admin">考评管理员</option>
                        <option value="hr_admin">HR管理员</option>
                        <option value="system_admin">系统管理员</option>
                    </select>
                </div>
                <div class="mb-4">
                    <p class="text-sm text-gray-600">将选中的 <span id="batchRoleCount">0</span> 个员工设置为所选角色</p>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeBatchRoleModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">取消</button>
                <button onclick="confirmBatchRole()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">确认分配</button>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Multi-select functionality -->
<script>
    // 多选功能变量
    let selectedStaffIds = new Set();
    
    // DOM加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializeMultiSelect();
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(e) {
            const dropdown = document.getElementById('batchActionsDropdown');
            const menu = document.getElementById('batchActionsMenu');
            if (!dropdown.contains(e.target)) {
                menu.classList.add('hidden');
            }
        });
    });
    
    // 初始化多选功能
    function initializeMultiSelect() {
        // 全选复选框事件
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const staffCheckboxes = document.querySelectorAll('.staff-checkbox');
                staffCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                    if (this.checked) {
                        selectedStaffIds.add(checkbox.value);
                    } else {
                        selectedStaffIds.delete(checkbox.value);
                    }
                });
                updateBatchToolbar();
            });
        }
        
        // 单个复选框事件
        const staffCheckboxes = document.querySelectorAll('.staff-checkbox');
        staffCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    selectedStaffIds.add(this.value);
                } else {
                    selectedStaffIds.delete(this.value);
                }
                updateBatchToolbar();
                updateSelectAllState();
            });
        });
    }
    
    // 更新全选复选框状态
    function updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const staffCheckboxes = document.querySelectorAll('.staff-checkbox');
        const checkedBoxes = document.querySelectorAll('.staff-checkbox:checked');
        
        if (checkedBoxes.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedBoxes.length === staffCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }
    
    // 更新批量操作工具栏
    function updateBatchToolbar() {
        const toolbar = document.getElementById('batchToolbar');
        const selectedCount = document.getElementById('selectedCount');
        const count = selectedStaffIds.size;
        
        selectedCount.textContent = count;
        
        if (count > 0) {
            toolbar.classList.remove('hidden');
            toolbar.classList.add('animate-fade-in');
        } else {
            toolbar.classList.add('hidden');
            toolbar.classList.remove('animate-fade-in');
        }
    }
    
    // 清空选择
    function clearSelection() {
        selectedStaffIds.clear();
        document.querySelectorAll('.staff-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        document.getElementById('selectAll').checked = false;
        document.getElementById('selectAll').indeterminate = false;
        updateBatchToolbar();
    }
    
    // 切换批量操作下拉菜单
    function toggleBatchActionsDropdown() {
        const menu = document.getElementById('batchActionsMenu');
        menu.classList.toggle('hidden');
    }
    
    // 批量更改状态
    function batchChangeStatus(isActive) {
        if (selectedStaffIds.size === 0) {
            showNotification('请先选择要操作的员工', 'warning');
            return;
        }
        
        const action = isActive ? '启用' : '禁用';
        const message = `确定要${action}选中的 ${selectedStaffIds.size} 个员工吗？`;
        
        if (confirm(message)) {
            const staffIds = Array.from(selectedStaffIds);
            
            // 显示加载状态
            showNotification(`正在${action}员工...`, 'info');
            
            fetch('{% url "organizations:admin:staff_batch_status" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    staff_ids: staffIds,
                    is_active: isActive
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(`成功${action} ${data.updated_count} 个员工`, 'success');
                    location.reload();
                } else {
                    showNotification(data.message || `${action}失败`, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('网络错误，请重试', 'error');
            });
        }
    }
    
    // 批量删除
    function batchDelete() {
        if (selectedStaffIds.size === 0) {
            showNotification('请先选择要删除的员工', 'warning');
            return;
        }
        
        const message = `确定要删除选中的 ${selectedStaffIds.size} 个员工吗？\n\n此操作不可撤销！`;
        
        if (confirm(message)) {
            const staffIds = Array.from(selectedStaffIds);
            
            showNotification('正在删除员工...', 'info');
            
            fetch('{% url "organizations:admin:staff_batch_delete" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    staff_ids: staffIds
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(`成功删除 ${data.deleted_count} 个员工`, 'success');
                    location.reload();
                } else {
                    showNotification(data.message || '删除失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('网络错误，请重试', 'error');
            });
        }
    }
    
    // 批量分配部门
    function batchChangeDepartment() {
        if (selectedStaffIds.size === 0) {
            showNotification('请先选择要操作的员工', 'warning');
            return;
        }
        
        document.getElementById('batchDeptCount').textContent = selectedStaffIds.size;
        document.getElementById('batchDepartmentModal').classList.remove('hidden');
    }
    
    function closeBatchDepartmentModal() {
        document.getElementById('batchDepartmentModal').classList.add('hidden');
    }
    
    function confirmBatchDepartment() {
        const departmentId = document.getElementById('batchDepartmentSelect').value;
        if (!departmentId) {
            showNotification('请选择部门', 'warning');
            return;
        }
        
        const staffIds = Array.from(selectedStaffIds);
        
        showNotification('正在分配部门...', 'info');
        
        fetch('{% url "organizations:admin:staff_batch_department" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                staff_ids: staffIds,
                department_id: departmentId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`成功分配 ${data.updated_count} 个员工到新部门`, 'success');
                closeBatchDepartmentModal();
                location.reload();
            } else {
                showNotification(data.message || '分配失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('网络错误，请重试', 'error');
        });
    }
    
    // 批量分配角色
    function batchChangeRole() {
        if (selectedStaffIds.size === 0) {
            showNotification('请先选择要操作的员工', 'warning');
            return;
        }
        
        document.getElementById('batchRoleCount').textContent = selectedStaffIds.size;
        document.getElementById('batchRoleModal').classList.remove('hidden');
    }
    
    function closeBatchRoleModal() {
        document.getElementById('batchRoleModal').classList.add('hidden');
    }
    
    function confirmBatchRole() {
        const role = document.getElementById('batchRoleSelect').value;
        if (!role) {
            showNotification('请选择角色', 'warning');
            return;
        }
        
        const staffIds = Array.from(selectedStaffIds);
        
        showNotification('正在分配角色...', 'info');
        
        fetch('{% url "organizations:admin:staff_batch_role" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                staff_ids: staffIds,
                role: role
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`成功分配 ${data.updated_count} 个员工为新角色`, 'success');
                closeBatchRoleModal();
                location.reload();
            } else {
                showNotification(data.message || '分配失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('网络错误，请重试', 'error');
        });
    }
    
    // 批量导出
    function batchExport() {
        if (selectedStaffIds.size === 0) {
            showNotification('请先选择要导出的员工', 'warning');
            return;
        }
        
        const staffIds = Array.from(selectedStaffIds);
        
        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "organizations:admin:staff_batch_export" %}';
        
        // 添加CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = getCookie('csrftoken');
        form.appendChild(csrfInput);
        
        // 添加员工ID
        staffIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'staff_ids';
            input.value = id;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
        
        showNotification(`正在导出 ${selectedStaffIds.size} 个员工的信息...`, 'success');
    }
    
    // 获取Cookie值
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    // 通知函数
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.innerHTML = `
            <div class="flex items-center space-x-3">
                <div class="flex-1">
                    <p class="text-sm font-medium">${message}</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
        
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }
</script>

<style>
    @keyframes fade-in {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .animate-fade-in {
        animation: fade-in 0.3s ease-out;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        filterStaff();
    });

    // 部门筛选
    document.getElementById('departmentFilter').addEventListener('change', function() {
        filterStaff();
    });

    // 职位筛选
    document.getElementById('positionFilter').addEventListener('change', function() {
        filterStaff();
    });

    // 自动聚焦搜索框
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.focus();
    }
});

function filterStaff() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const departmentFilter = document.getElementById('departmentFilter').value;
    const positionFilter = document.getElementById('positionFilter').value;
    const staffRows = document.querySelectorAll('tbody tr');
    let visibleCount = 0;
    
    staffRows.forEach(row => {
        const staffName = row.querySelector('.text-sm.font-medium.text-gray-900')?.textContent.toLowerCase() || '';
        const staffNo = row.querySelector('.text-sm.text-gray-500')?.textContent.toLowerCase() || '';
        const departmentId = row.getAttribute('data-department');
        const positionLevel = parseInt(row.getAttribute('data-position')) || 0;
        
        let showRow = true;
        
        // 搜索筛选
        if (searchTerm && !staffName.includes(searchTerm) && !staffNo.includes(searchTerm)) {
            showRow = false;
        }
        
        // 部门筛选
        if (departmentFilter && departmentId !== departmentFilter) {
            showRow = false;
        }
        
        // 职位筛选
        if (positionFilter) {
            if (positionFilter === 'management' && positionLevel < 6) {
                showRow = false;
            } else if (positionFilter === 'regular' && positionLevel >= 6) {
                showRow = false;
            }
        }
        
        row.style.display = showRow ? '' : 'none';
        if (showRow) visibleCount++;
    });
}

// 删除确认 - 使用统一删除管理器
function confirmDelete(staffId, staffName) {
    window.confirmDelete({
        title: '删除员工',
        message: `确定要删除员工 "${staffName}" 吗？该员工的所有相关数据将被标记为删除状态。`,
        url: `/organizations/admin/staff/${staffId}/delete/`,
        method: 'POST',
        onSuccess: function(result) {
            // 成功后重新加载页面或移除行
            location.reload();
        },
        onError: function(error) {
            console.error('删除员工失败:', error);
        }
    });
}
</script>
{% endblock %}
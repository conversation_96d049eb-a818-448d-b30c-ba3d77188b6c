{% extends "admin/base_admin.html" %}
{% load url_tags %}

{% block page_title %}职位管理{% endblock %}
{% block page_description %}管理企业职位信息，配置职位层级和权限{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <!-- 视图切换按钮 -->
    <div class="flex bg-gray-100 rounded-lg p-1">
        <button id="table-view-btn" class="px-3 py-1 text-sm font-medium rounded-md bg-white text-gray-900 shadow-sm">
            <i data-lucide="list" class="w-4 h-4 mr-1 inline"></i>
            表格视图
        </button>
        <button id="card-view-btn" class="px-3 py-1 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900">
            <i data-lucide="grid-3x3" class="w-4 h-4 mr-1 inline"></i>
            卡片视图
        </button>
    </div>
    
    <!-- 导入按钮 -->
    <a href="{% url 'organizations:admin:position_import' %}" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="upload" class="w-4 h-4"></i>
        <span>导入</span>
    </a>

    <!-- 导出按钮 -->
    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>导出</span>
    </button>

    <!-- 新建按钮 -->
    <a href="{% url 'organizations:admin:position_create' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="plus" class="w-4 h-4"></i>
        <span>新建职位</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<!-- 统计卡片 -->
<div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="briefcase" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总职位数</p>
                <p class="text-2xl font-semibold text-gray-900">{{ object_list|length }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="crown" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">管理岗位</p>
                <p class="text-2xl font-semibold text-gray-900">{{ management_count|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="user-check" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">部门经理</p>
                <p class="text-2xl font-semibold text-gray-900">{{ department_manager_count|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">在职人员</p>
                <p class="text-2xl font-semibold text-gray-900">{{ total_staff|default:0 }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-orange-100 rounded-lg">
                <i data-lucide="building" class="w-6 h-6 text-orange-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">涉及部门</p>
                <p class="text-2xl font-semibold text-gray-900">{{ departments.count }}</p>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="bg-white rounded-lg shadow mb-6 p-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="relative">
            <input type="text" id="searchInput" placeholder="搜索职位名称或编码..." 
                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
        </div>
        
        <select id="departmentFilter" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">所有部门</option>
            {% for dept in departments %}
            <option value="{{ dept.id }}">{{ dept.name }}</option>
            {% endfor %}
        </select>
        
        <select id="levelFilter" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">所有级别</option>
            <option value="1-4">1-4级（基础岗位）</option>
            <option value="5-6">5-6级（中级岗位）</option>
            <option value="7-8">7-8级（高级岗位）</option>
            <option value="9">9级（专家岗位）</option>
        </select>
        
        <select id="typeFilter" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">所有类型</option>
            <option value="management">管理岗</option>
            <option value="regular">普通岗</option>
        </select>
    </div>
    
    <!-- 快速筛选标签 -->
    <div class="flex flex-wrap gap-2 mt-4">
        <button class="filter-tag px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200" data-filter="all">
            全部职位
        </button>
        <button class="filter-tag px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200" data-filter="active">
            启用职位
        </button>
        <button class="filter-tag px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200" data-filter="management">
            管理岗位
        </button>
        <button class="filter-tag px-3 py-1 text-sm bg-yellow-100 text-yellow-700 rounded-full hover:bg-yellow-200" data-filter="department-manager">
            部门经理
        </button>
        <button class="filter-tag px-3 py-1 text-sm bg-green-100 text-green-700 rounded-full hover:bg-green-200" data-filter="high-level">
            高级职位
        </button>
    </div>
</div>

<!-- 表格视图 -->
<div class="bg-white rounded-lg shadow overflow-hidden">
    <!-- 表格头部工具栏 -->
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <input type="checkbox" id="selectAll" class="checkbox">
                    <label for="selectAll" class="ml-2 text-sm text-gray-700">全选</label>
                </div>
                <span class="text-sm text-gray-500" id="selectedCount">已选择 0 项</span>
            </div>
            
            <div class="flex items-center space-x-2">
                <!-- 批量操作 -->
                <div class="hidden" id="batchActions">
                    <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">
                        批量编辑
                    </button>
                    <button class="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700">
                        批量删除
                    </button>
                </div>
                
                <!-- 排序选项 -->
                <select id="sortBy" class="text-sm border border-gray-300 rounded px-2 py-1">
                    <option value="name">按名称排序</option>
                    <option value="level">按级别排序</option>
                    <option value="department">按部门排序</option>
                    <option value="created_at">按创建时间排序</option>
                </select>
                
                <!-- 显示数量 -->
                <select id="pageSize" class="text-sm border border-gray-300 rounded px-2 py-1">
                    <option value="10">显示 10 条</option>
                    <option value="25" selected>显示 25 条</option>
                    <option value="50">显示 50 条</option>
                    <option value="100">显示 100 条</option>
                </select>
            </div>
        </div>
    </div>
    
    <!-- 表格内容 -->
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200" id="positionsTable">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input type="checkbox" class="checkbox">
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="name">
                        <div class="flex items-center space-x-1">
                            <span>职位信息</span>
                            <i data-lucide="arrow-up-down" class="w-3 h-3"></i>
                        </div>
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="department">
                        <div class="flex items-center space-x-1">
                            <span>所属部门</span>
                            <i data-lucide="arrow-up-down" class="w-3 h-3"></i>
                        </div>
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="level">
                        <div class="flex items-center space-x-1">
                            <span>职位级别</span>
                            <i data-lucide="arrow-up-down" class="w-3 h-3"></i>
                        </div>
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        在职人数
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="created_at">
                        <div class="flex items-center space-x-1">
                            <span>创建时间</span>
                            <i data-lucide="arrow-up-down" class="w-3 h-3"></i>
                        </div>
                    </th>
                    <th scope="col" class="relative px-3 py-3">
                        <span class="sr-only">操作</span>
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="positionsTableBody">
                {% if object_list %}
                    {% for position in object_list %}
                    <tr class="hover:bg-gray-50 position-row"
                        data-department="{{ position.department.id }}"
                        data-level="{{ position.level }}"
                        data-management="{{ position.is_management|yesno:'true,false' }}"
                        data-department-manager="{{ position.is_department_manager|yesno:'true,false' }}"
                        data-active="{{ position.is_active|yesno:'true,false' }}">
                        <td class="px-3 py-4 whitespace-nowrap">
                            <input type="checkbox" class="row-checkbox checkbox" value="{{ position.pk }}">
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    {% if position.is_management %}
                                        <div class="p-2 bg-purple-100 rounded-lg">
                                            <i data-lucide="crown" class="w-4 h-4 text-purple-600"></i>
                                        </div>
                                    {% else %}
                                        <div class="p-2 bg-blue-100 rounded-lg">
                                            <i data-lucide="briefcase" class="w-4 h-4 text-blue-600"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ position.name }}</div>
                                    <div class="text-sm text-gray-500">{{ position.position_code }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ position.department.name }}</div>
                            <div class="text-sm text-gray-500">{{ position.department.dept_code }}</div>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if position.level >= 9 %}bg-red-100 text-red-800
                                {% elif position.level >= 7 %}bg-purple-100 text-purple-800
                                {% elif position.level >= 5 %}bg-blue-100 text-blue-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ position.level }}级
                            </span>
                            {% if position.is_management %}
                                <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    管理岗
                                </span>
                            {% endif %}
                            {% if position.is_department_manager %}
                                <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    部门经理
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div class="flex items-center">
                                <i data-lucide="users" class="w-4 h-4 text-gray-400 mr-1"></i>
                                {{ position.get_staff_count|default:0 }}
                            </div>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap">
                            {% if position.is_active %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                    启用
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>
                                    禁用
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ position.created_at|date:"Y-m-d" }}
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                <a href="{% url 'organizations:admin:position_detail' position.pk %}" 
                                   class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="查看详情">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'organizations:admin:position_update' position.pk %}" 
                                   class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="编辑">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <button onclick="confirmDelete('{{ position.pk }}', '{{ position.name }}')" 
                                        class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="删除">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="8" class="px-3 py-12 text-center">
                            <div class="flex flex-col items-center">
                                <i data-lucide="briefcase" class="w-12 h-12 text-gray-400 mb-4"></i>
                                <h3 class="text-sm font-medium text-gray-900 mb-2">暂无职位数据</h3>
                                <p class="text-sm text-gray-500 mb-4">开始创建第一个职位吧</p>
                                <a href="{% url 'organizations:admin:position_create' %}" 
                                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                    新建职位
                                </a>
                            </div>
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
    
    <!-- 表格底部分页 -->
    {% if is_paginated %}
    <div class="bg-white px-6 py-3 flex items-center justify-between border-t border-gray-200">
        <div class="flex-1 flex justify-between sm:hidden">
            {% if page_obj.has_previous %}
                <a href="{% current_url_with_page page_obj.previous_page_number %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    上一页
                </a>
            {% endif %}
            {% if page_obj.has_next %}
                <a href="{% current_url_with_page page_obj.next_page_number %}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    下一页
                </a>
            {% endif %}
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    显示第 <span class="font-medium">{{ page_obj.start_index }}</span> 到 <span class="font-medium">{{ page_obj.end_index }}</span> 条，
                    共 <span class="font-medium">{{ paginator.count }}</span> 条记录
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if page_obj.has_previous %}
                        <a href="{% current_url_with_page page_obj.previous_page_number %}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i data-lucide="chevron-left" class="w-4 h-4"></i>
                        </a>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                {{ num }}
                            </span>
                        {% else %}
                            <a href="{% current_url_with_page num %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <a href="{% current_url_with_page page_obj.next_page_number %}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i data-lucide="chevron-right" class="w-4 h-4"></i>
                        </a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化
    initializeTable();
    initializeFilters();
    initializeViewSwitcher();
    initializeBatchActions();

    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

// 表格初始化
function initializeTable() {
    const table = document.getElementById('positionsTable');
    const selectAllCheckbox = document.getElementById('selectAll');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');

    // 全选功能
    selectAllCheckbox?.addEventListener('change', function() {
        rowCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
        toggleBatchActions();
    });

    // 行选择功能
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();
            toggleBatchActions();

            // 更新全选状态
            const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === rowCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
        });
    });

    // 排序功能
    const sortHeaders = document.querySelectorAll('[data-sort]');
    sortHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const sortBy = this.getAttribute('data-sort');
            sortTable(sortBy);
        });
    });
}

// 筛选功能初始化
function initializeFilters() {
    const searchInput = document.getElementById('searchInput');
    const departmentFilter = document.getElementById('departmentFilter');
    const levelFilter = document.getElementById('levelFilter');
    const typeFilter = document.getElementById('typeFilter');
    const filterTags = document.querySelectorAll('.filter-tag');

    // 搜索功能
    searchInput?.addEventListener('input', debounce(filterTable, 300));

    // 下拉筛选
    [departmentFilter, levelFilter, typeFilter].forEach(filter => {
        filter?.addEventListener('change', filterTable);
    });

    // 快速筛选标签
    filterTags.forEach(tag => {
        tag.addEventListener('click', function() {
            // 移除其他标签的激活状态
            filterTags.forEach(t => t.classList.remove('bg-blue-500', 'text-white'));
            // 激活当前标签
            this.classList.add('bg-blue-500', 'text-white');

            const filterType = this.getAttribute('data-filter');
            applyQuickFilter(filterType);
        });
    });
}

// 视图切换功能
function initializeViewSwitcher() {
    const tableViewBtn = document.getElementById('table-view-btn');
    const cardViewBtn = document.getElementById('card-view-btn');

    cardViewBtn?.addEventListener('click', function() {
        // 跳转到卡片视图，保持当前页码和其他参数
        const url = new URL(window.location);
        url.searchParams.set('view', 'card'); // 设置view参数为card
        window.location.href = url.toString();
    });
}

// 批量操作功能
function initializeBatchActions() {
    const batchActions = document.getElementById('batchActions');
    // 这里可以添加批量操作的具体实现
}

// 更新选中数量
function updateSelectedCount() {
    const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
    const selectedCountElement = document.getElementById('selectedCount');
    if (selectedCountElement) {
        selectedCountElement.textContent = `已选择 ${checkedCount} 项`;
    }
}

// 切换批量操作按钮显示
function toggleBatchActions() {
    const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
    const batchActions = document.getElementById('batchActions');

    if (batchActions) {
        if (checkedCount > 0) {
            batchActions.classList.remove('hidden');
        } else {
            batchActions.classList.add('hidden');
        }
    }
}

// 表格筛选
function filterTable() {
    const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
    const departmentFilter = document.getElementById('departmentFilter')?.value || '';
    const levelFilter = document.getElementById('levelFilter')?.value || '';
    const typeFilter = document.getElementById('typeFilter')?.value || '';

    const rows = document.querySelectorAll('.position-row');
    let visibleCount = 0;

    rows.forEach(row => {
        const positionName = row.querySelector('.text-sm.font-medium')?.textContent.toLowerCase() || '';
        const positionCode = row.querySelector('.text-sm.text-gray-500')?.textContent.toLowerCase() || '';
        const departmentId = row.getAttribute('data-department');
        const positionLevel = parseInt(row.getAttribute('data-level'));
        const isManagement = row.getAttribute('data-management') === 'true';

        let showRow = true;

        // 搜索筛选
        if (searchTerm && !positionName.includes(searchTerm) && !positionCode.includes(searchTerm)) {
            showRow = false;
        }

        // 部门筛选
        if (departmentFilter && departmentId !== departmentFilter) {
            showRow = false;
        }

        // 级别筛选
        if (levelFilter) {
            switch (levelFilter) {
                case '1-4':
                    if (positionLevel < 1 || positionLevel > 4) showRow = false;
                    break;
                case '5-6':
                    if (positionLevel < 5 || positionLevel > 6) showRow = false;
                    break;
                case '7-8':
                    if (positionLevel < 7 || positionLevel > 8) showRow = false;
                    break;
                case '9':
                    if (positionLevel !== 9) showRow = false;
                    break;
            }
        }

        // 类型筛选
        if (typeFilter) {
            if (typeFilter === 'management' && !isManagement) showRow = false;
            if (typeFilter === 'regular' && isManagement) showRow = false;
        }

        row.style.display = showRow ? '' : 'none';
        if (showRow) visibleCount++;
    });

    // 更新显示计数
    updateFilterCount(visibleCount);
}

// 快速筛选
function applyQuickFilter(filterType) {
    const rows = document.querySelectorAll('.position-row');

    rows.forEach(row => {
        const isActive = row.getAttribute('data-active') === 'true';
        const isManagement = row.getAttribute('data-management') === 'true';
        const isDepartmentManager = row.getAttribute('data-department-manager') === 'true';
        const positionLevel = parseInt(row.getAttribute('data-level'));

        let showRow = true;

        switch (filterType) {
            case 'all':
                showRow = true;
                break;
            case 'active':
                showRow = isActive;
                break;
            case 'management':
                showRow = isManagement;
                break;
            case 'department-manager':
                showRow = isDepartmentManager;
                break;
            case 'high-level':
                showRow = positionLevel >= 7;
                break;
        }

        row.style.display = showRow ? '' : 'none';
    });
}

// 表格排序
function sortTable(sortBy) {
    const tbody = document.getElementById('positionsTableBody');
    const rows = Array.from(tbody.querySelectorAll('.position-row'));

    rows.sort((a, b) => {
        let aValue, bValue;

        switch (sortBy) {
            case 'name':
                aValue = a.querySelector('.text-sm.font-medium').textContent;
                bValue = b.querySelector('.text-sm.font-medium').textContent;
                break;
            case 'department':
                aValue = a.querySelector('td:nth-child(3) .text-sm.text-gray-900').textContent;
                bValue = b.querySelector('td:nth-child(3) .text-sm.text-gray-900').textContent;
                break;
            case 'level':
                aValue = parseInt(a.getAttribute('data-level'));
                bValue = parseInt(b.getAttribute('data-level'));
                return bValue - aValue; // 降序
            case 'created_at':
                aValue = a.querySelector('td:nth-child(7)').textContent;
                bValue = b.querySelector('td:nth-child(7)').textContent;
                break;
        }

        if (typeof aValue === 'string') {
            return aValue.localeCompare(bValue);
        }
        return aValue - bValue;
    });

    // 重新排列行
    rows.forEach(row => tbody.appendChild(row));
}

// 更新筛选计数
function updateFilterCount(count) {
    console.log(`显示 ${count} 条记录`);
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 删除确认
function confirmDelete(positionId, positionName) {
    if (confirm(`确定要删除职位 "${positionName}" 吗？此操作不可撤销。`)) {
        window.location.href = `/admin/positions/${positionId}/delete/`;
    }
}
</script>
{% endblock %}

{% extends "admin/base_admin.html" %}

{% block page_title %}考评批次管理{% endblock %}
{% block page_description %}创建和管理考评活动批次，控制考评周期和进度{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:batch_create' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
    <i data-lucide="plus" class="w-4 h-4"></i>
    <span>新建批次</span>
</a>
{% endblock %}

{% block admin_content %}
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="calendar" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总批次数</p>
                <p class="text-2xl font-bold text-gray-900">{{ batches|length|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="play-circle" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">进行中</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% for batch in batches %}{% if batch.status == 'active' %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                </p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="edit" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">草稿</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% for batch in batches %}{% if batch.status == 'draft' %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                </p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="check-circle" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">已完成</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% for batch in batches %}{% if batch.status == 'completed' %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i data-lucide="search" class="h-4 w-4 text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" 
                           class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500" 
                           placeholder="搜索批次名称或描述...">
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有状态</option>
                    <option value="draft">草稿</option>
                    <option value="active">进行中</option>
                    <option value="completed">已完成</option>
                    <option value="cancelled">已取消</option>
                </select>
                <select id="timeFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有时间</option>
                    <option value="this_month">本月</option>
                    <option value="last_month">上月</option>
                    <option value="this_quarter">本季度</option>
                    <option value="this_year">今年</option>
                </select>
                <div class="flex items-center">
                    <input type="checkbox" id="showDisabledFilter" class="checkbox mr-2" 
                           onchange="toggleShowDisabled()" 
                           {% if show_disabled == 'true' %}checked{% endif %}>
                    <label for="showDisabledFilter" class="text-sm text-gray-700 cursor-pointer">
                        显示已禁用
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Batches List -->
<div class="space-y-4" id="batchesList">
    {% for batch in batches %}
        <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow batch-card border border-gray-200"
             data-status="{{ batch.status }}" 
             data-date="{{ batch.start_date|date:'Y-m-d' }}">
            
            <div class="px-6 py-6">
                <div class="flex items-start justify-between">
                    <!-- 批次信息 -->
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <h3 class="text-xl font-semibold text-gray-900">{{ batch.name }}</h3>
                            
                            <!-- 状态标签 -->
                            {% if batch.status == 'draft' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i data-lucide="edit" class="w-3 h-3 mr-1"></i>
                                    草稿
                                </span>
                            {% elif batch.status == 'active' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i data-lucide="play-circle" class="w-3 h-3 mr-1"></i>
                                    进行中
                                </span>
                            {% elif batch.status == 'completed' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                    已完成
                                </span>
                            {% elif batch.status == 'cancelled' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>
                                    已取消
                                </span>
                            {% endif %}
                        </div>
                        
                        <p class="text-gray-600 mb-4">{{ batch.description|default:"暂无描述"|truncatechars:100 }}</p>
                        
                        <!-- 批次详情 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                            <div class="flex items-center text-gray-600">
                                <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                                <span>模板：{{ batch.default_template.name }}</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                                <span>{{ batch.start_date|date:"m-d" }} ~ {{ batch.end_date|date:"m-d" }}</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                                <span>参与：{{ batch.get_participants_count.total }} 人</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i data-lucide="target" class="w-4 h-4 mr-2"></i>
                                <span>进度：{{ batch.get_progress }}%</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="flex items-center space-x-2 ml-6">
                        {% if batch.status == 'draft' %}
                            <a href="{% url 'evaluations:admin:batch_activate' batch.pk %}" 
                               class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                               onclick="return confirm('确定要激活这个批次吗？激活后将开始考评流程。')">
                                <i data-lucide="play" class="w-4 h-4 inline mr-1"></i>
                                激活
                            </a>
                        {% elif batch.status == 'active' %}
                            <a href="{% url 'evaluations:admin:batch_complete' batch.pk %}" 
                               class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                               onclick="return confirm('确定要完成这个批次吗？完成后将无法继续评分。')">
                                <i data-lucide="check" class="w-4 h-4 inline mr-1"></i>
                                完成
                            </a>
                        {% endif %}
                        
                        <a href="{% url 'evaluations:admin:batch_detail' batch.pk %}" 
                           class="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50">
                            查看详情
                        </a>
                        
                        {% if batch.status == 'draft' %}
                            <a href="{% url 'evaluations:admin:batch_update' batch.pk %}" 
                               class="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50">
                                编辑
                            </a>
                        {% endif %}
                        
                        <a href="{% url 'evaluations:admin:batch_template_config' batch.pk %}" 
                           class="px-3 py-1 border border-blue-300 text-blue-700 text-sm rounded hover:bg-blue-50"
                           title="配置批次可用模板和关系类型映射">
                            <i data-lucide="settings" class="w-4 h-4 inline mr-1"></i>
                            模板配置
                        </a>
                        
                        <div class="relative">
                            <button type="button" onclick="toggleDropdown({{ batch.pk }})" 
                                    class="p-1 text-gray-400 hover:text-gray-600">
                                <i data-lucide="more-vertical" class="w-4 h-4"></i>
                            </button>
                            <div id="dropdown-{{ batch.pk }}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                                <div class="py-1">
                                    <a href="{% url 'evaluations:admin:batch_relations' batch.pk %}" 
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                                        管理评价关系
                                    </a>
                                    <a href="{% url 'evaluations:admin:progress_detail' batch.pk %}" 
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i data-lucide="bar-chart-3" class="w-4 h-4 mr-2"></i>
                                        查看进度
                                    </a>
                                    {% if batch.status == 'draft' %}
                                        <a href="{% url 'evaluations:admin:batch_assign' batch.pk %}" 
                                           class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i data-lucide="zap" class="w-4 h-4 mr-2"></i>
                                            智能分配
                                        </a>
                                        <div class="border-t border-gray-100"></div>
                                        <button onclick="deleteBatch({{ batch.pk }}, '{{ batch.name }}')" 
                                                class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                            <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                                            删除批次
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 进度条 -->
                {% if batch.status == 'active' or batch.status == 'completed' %}
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">完成进度</span>
                            <span class="text-sm text-gray-600">{{ batch.get_progress }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                 style="width: {{ batch.get_progress }}%"></div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    {% empty %}
        <!-- 空状态 -->
        <div class="text-center py-12">
            <i data-lucide="calendar-plus" class="mx-auto h-16 w-16 text-gray-400"></i>
            <h3 class="mt-4 text-lg font-medium text-gray-900">还没有考评批次</h3>
            <p class="mt-2 text-sm text-gray-500">创建第一个考评批次，开始组织考评活动。</p>
            <div class="mt-6">
                <a href="{% url 'evaluations:admin:batch_create' %}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    创建批次
                </a>
            </div>
        </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="mt-8 flex items-center justify-between">
    <div class="text-sm text-gray-700">
        显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 项，共 {{ paginator.count }} 项
    </div>
    <nav class="flex items-center space-x-2">
        {% if page_obj.has_previous %}
            <a href="?page=1" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">首页</a>
            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</a>
        {% endif %}
        
        <span class="px-3 py-2 text-sm bg-blue-50 border border-blue-200 rounded-md text-blue-600">
            {{ page_obj.number }}
        </span>
        
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</a>
            <a href="?page={{ paginator.num_pages }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">末页</a>
        {% endif %}
    </nav>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        filterBatches();
    });

    // 状态筛选
    document.getElementById('statusFilter').addEventListener('change', function() {
        filterBatches();
    });

    // 时间筛选
    document.getElementById('timeFilter').addEventListener('change', function() {
        filterBatches();
    });

    function filterBatches() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;
        const timeFilter = document.getElementById('timeFilter').value;
        const batchCards = document.querySelectorAll('.batch-card');

        batchCards.forEach(card => {
            const title = card.querySelector('h3').textContent.toLowerCase();
            const description = card.querySelector('p').textContent.toLowerCase();
            const status = card.dataset.status;
            const date = new Date(card.dataset.date);

            let showCard = true;

            // 文本搜索
            if (searchTerm && !title.includes(searchTerm) && !description.includes(searchTerm)) {
                showCard = false;
            }

            // 状态筛选
            if (statusFilter && status !== statusFilter) {
                showCard = false;
            }

            // 时间筛选
            if (timeFilter && !matchesTimeFilter(date, timeFilter)) {
                showCard = false;
            }

            card.style.display = showCard ? 'block' : 'none';
        });
    }

    function matchesTimeFilter(date, filter) {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
        const startOfQuarter = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        const startOfYear = new Date(now.getFullYear(), 0, 1);

        switch (filter) {
            case 'this_month':
                return date >= startOfMonth;
            case 'last_month':
                return date >= startOfLastMonth && date <= endOfLastMonth;
            case 'this_quarter':
                return date >= startOfQuarter;
            case 'this_year':
                return date >= startOfYear;
            default:
                return true;
        }
    }

    // 切换下拉菜单
    function toggleDropdown(batchId) {
        const dropdown = document.getElementById(`dropdown-${batchId}`);
        const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
        
        // 关闭其他下拉菜单
        allDropdowns.forEach(d => {
            if (d !== dropdown) {
                d.classList.add('hidden');
            }
        });
        
        // 切换当前下拉菜单
        dropdown.classList.toggle('hidden');
    }

    // 点击外部关闭下拉菜单
    document.addEventListener('click', function(event) {
        if (!event.target.closest('[onclick*="toggleDropdown"]') && !event.target.closest('[id^="dropdown-"]')) {
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(d => d.classList.add('hidden'));
        }
    });

    // 删除批次
    function deleteBatch(batchId, batchName) {
        if (confirm(`确定要删除批次"${batchName}"吗？此操作不可恢复，相关的评价关系和记录也将被删除。`)) {
            fetch(`/evaluations/admin/batches/${batchId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => {
                if (response.ok) {
                    showNotification('批次删除成功', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification('删除失败，请重试', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('删除失败，请重试', 'error');
            });
        }
    }

    // 显示/隐藏已禁用批次
    function toggleShowDisabled() {
        const checkbox = document.getElementById('showDisabledFilter');
        const showDisabled = checkbox.checked;
        
        const url = new URL(window.location);
        if (showDisabled) {
            url.searchParams.set('show_disabled', 'true');
        } else {
            url.searchParams.delete('show_disabled');
        }
        
        window.location.href = url.toString();
    }

    // 搜索框自动聚焦
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
    });
</script>
{% endblock %}